package grpcclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"fin_gateway/internal/types"
)

type IntentClient struct {
	baseURL    string
	httpClient *http.Client
}

func New(addr string) (*IntentClient, error) {
	// Convert gRPC address to HTTP URL for now
	// This should be updated when actual gRPC service is available
	baseURL := fmt.Sprintf("http://%s", addr)

	return &IntentClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 5 * time.Second,
		},
	}, nil
}

func (c *IntentClient) DetectIntent(req *types.IntentRequest) (*types.IntentResponse, error) {
	// For now, return a mock response
	// This should be replaced with actual HTTP/gRPC call to fin_work_intent

	// Mock response based on the request
	mockResponse := &types.IntentResponse{
		Intent: &types.Intent{
			Label:      "mock_intent",
			Confidence: 0.85,
			Meta: &types.IntentMeta{
				Route:       "/api/mock",
				Aggregation: "sum",
				Granularity: "daily",
				Dimensions:  []string{"time", "category"},
				Metrics:     []string{"count", "value"},
				Entities:    []string{"user", "session"},
			},
		},
	}

	return mockResponse, nil
}

// TODO: Implement actual HTTP/gRPC call when fin_work_intent is available
func (c *IntentClient) detectIntentHTTP(req *types.IntentRequest) (*types.IntentResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := c.httpClient.Post(
		c.baseURL+"/detect-intent",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	var intentResp types.IntentResponse
	if err := json.NewDecoder(resp.Body).Decode(&intentResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &intentResp, nil
}

func (c *IntentClient) Close() error {
	// No connection to close for HTTP client
	return nil
}
