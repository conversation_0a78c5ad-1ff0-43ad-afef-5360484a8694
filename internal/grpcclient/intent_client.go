package grpcclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"fin_gateway/internal/types"

	"go.uber.org/zap"
)

type IntentClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *zap.Logger
}

func New(addr string, logger *zap.Logger) (*IntentClient, error) {
	// Convert gRPC address to HTTP URL for now
	// This should be updated when actual gRPC service is available
	baseURL := fmt.Sprintf("http://%s", addr)

	return &IntentClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 5 * time.Second,
		},
		logger: logger,
	}, nil
}

func (c *IntentClient) DetectIntent(req *types.IntentRequest) (*types.IntentResponse, error) {
	// Use the actual HTTP implementation to call fin_work_intent service
	return c.detectIntentHTTP(req)
}

// detectIntentHTTP makes HTTP call to fin_work_intent service
func (c *IntentClient) detectIntentHTTP(req *types.IntentRequest) (*types.IntentResponse, error) {
	endpoint := c.baseURL + "/detect-intent"

	c.logger.Info("making intent detection request",
		zap.String("endpoint", endpoint),
		zap.String("request_id", req.Id),
		zap.Int("message_count", len(req.Messages)),
	)

	jsonData, err := json.Marshal(req)
	if err != nil {
		c.logger.Error("failed to marshal intent request",
			zap.String("request_id", req.Id),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := c.httpClient.Post(
		endpoint,
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		c.logger.Error("failed to make HTTP request to intent service",
			zap.String("endpoint", endpoint),
			zap.String("request_id", req.Id),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to make HTTP request to intent service at %s: %w", endpoint, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		c.logger.Error("intent service returned non-200 status",
			zap.String("endpoint", endpoint),
			zap.String("request_id", req.Id),
			zap.Int("status_code", resp.StatusCode),
		)
		return nil, fmt.Errorf("intent service returned status %d from %s", resp.StatusCode, endpoint)
	}

	var intentResp types.IntentResponse
	if err := json.NewDecoder(resp.Body).Decode(&intentResp); err != nil {
		c.logger.Error("failed to decode intent response",
			zap.String("request_id", req.Id),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	c.logger.Info("successfully received intent response",
		zap.String("request_id", req.Id),
		zap.String("intent_label", intentResp.Intent.Label),
		zap.Float64("confidence", intentResp.Intent.Confidence),
	)

	return &intentResp, nil
}

func (c *IntentClient) Close() error {
	// No connection to close for HTTP client
	return nil
}
