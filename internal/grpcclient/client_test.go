package grpcclient

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"fin_gateway/internal/types"
	"go.uber.org/zap"
)

func TestDetectIntent_DifferentQuestions(t *testing.T) {
	logger := zap.NewNop()

	testCases := []struct {
		name           string
		question       string
		mockResponse   *types.IntentResponse
		expectedIntent string
	}{
		{
			name:     "Product List Request",
			question: "berikan list products",
			mockResponse: &types.IntentResponse{
				Intent: &types.Intent{
					Label:      "product_list",
					Confidence: 0.92,
					Meta: &types.IntentMeta{
						Route:       "/api/products",
						Aggregation: "list",
						Granularity: "item",
						Dimensions:  []string{"product", "category"},
						Metrics:     []string{"count", "availability"},
						Entities:    []string{"product", "catalog"},
					},
				},
			},
			expectedIntent: "product_list",
		},
		{
			name:     "Summary Awareness Request",
			question: "berikan summary awareness per category setiap tahun",
			mockResponse: &types.IntentResponse{
				Intent: &types.Intent{
					Label:      "awareness_summary",
					Confidence: 0.88,
					Meta: &types.IntentMeta{
						Route:       "/api/analytics/awareness",
						Aggregation: "sum",
						Granularity: "yearly",
						Dimensions:  []string{"time", "category", "awareness"},
						Metrics:     []string{"awareness_score", "trend"},
						Entities:    []string{"category", "year", "awareness"},
					},
				},
			},
			expectedIntent: "awareness_summary",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if r.Method != "POST" {
					t.Errorf("Expected POST request, got %s", r.Method)
				}
				if r.URL.Path != "/detect-intent" {
					t.Errorf("Expected /detect-intent path, got %s", r.URL.Path)
				}

				var req types.IntentRequest
				if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
					t.Errorf("Failed to decode request: %v", err)
					return
				}

				if len(req.Messages) == 0 || req.Messages[0].Content != tc.question {
					t.Errorf("Expected question '%s', got '%s'", tc.question, req.Messages[0].Content)
				}

				w.Header().Set("Content-Type", "application/json")
				json.NewEncoder(w).Encode(tc.mockResponse)
			}))
			defer server.Close()

			client, err := New(server.URL[7:], logger)
			if err != nil {
				t.Fatalf("Failed to create client: %v", err)
			}

			req := &types.IntentRequest{
				Id: "test_" + tc.name,
				Messages: []*types.Message{
					{
						Role:    "user",
						Content: tc.question,
						Parts: []*types.MessagePart{
							{Type: "text", Text: tc.question},
						},
					},
				},
			}

			resp, err := client.DetectIntent(req)
			if err != nil {
				t.Fatalf("DetectIntent failed: %v", err)
			}

			if resp.Intent.Label != tc.expectedIntent {
				t.Errorf("Expected intent '%s', got '%s'", tc.expectedIntent, resp.Intent.Label)
			}

			if resp.Intent.Meta.Route == "/api/mock" {
				t.Error("Still receiving mock response - implementation not working correctly")
			}
		})
	}
}

func TestDetectIntent_ServiceUnavailable(t *testing.T) {
	logger := zap.NewNop()

	client, err := New("localhost:99999", logger)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	req := &types.IntentRequest{
		Id: "test_unavailable",
		Messages: []*types.Message{
			{
				Role:    "user",
				Content: "test question",
				Parts: []*types.MessagePart{
					{Type: "text", Text: "test question"},
				},
			},
		},
	}

	_, err = client.DetectIntent(req)
	if err == nil {
		t.Error("Expected error when service is unavailable, but got none")
	}

	if err != nil && len(err.Error()) == 0 {
		t.Error("Error message should not be empty")
	}
}
