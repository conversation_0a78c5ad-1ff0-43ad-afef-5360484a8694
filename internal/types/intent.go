package types

// Placeholder types for intent service communication
// These should be replaced with actual protobuf imports from fin_work_intent when available

type MessagePart struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

type Message struct {
	Role    string         `json:"role"`
	Content string         `json:"content"`
	Parts   []*MessagePart `json:"parts"`
}

type IntentRequest struct {
	Id        string     `json:"id"`
	Messages  []*Message `json:"messages"`
	UserId    string     `json:"userId"`
	SessionId string     `json:"sessionId"`
	UserEmail string     `json:"userEmail"`
}

type IntentMeta struct {
	Route       string   `json:"route"`
	Aggregation string   `json:"aggregation"`
	Granularity string   `json:"granularity"`
	Dimensions  []string `json:"dimensions"`
	Metrics     []string `json:"metrics"`
	Entities    []string `json:"entities"`
}

type Intent struct {
	Label      string      `json:"label"`
	Confidence float64     `json:"confidence"`
	Meta       *IntentMeta `json:"meta"`
}

type IntentResponse struct {
	Intent *Intent `json:"intent"`
}

// IntentServiceClient interface for gRPC client
// This should be replaced with actual protobuf generated client when available
type IntentServiceClient interface {
	DetectIntent(req *IntentRequest) (*IntentResponse, error)
}
