package rabbitmq

import (
	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

type Consumer struct {
	conn    *amqp.Connection
	channel *amqp.Channel
	logger  *zap.Logger
}

func NewConsumer(amqpURL string, logger *zap.Logger) (*Consumer, error) {
	conn, err := amqp.Dial(amqpURL)
	if err != nil {
		return nil, err
	}
	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}
	return &Consumer{conn: conn, channel: ch, logger: logger}, nil
}

func (c *Consumer) Subscribe(queue string, handler func([]byte)) error {
	_, err := c.channel.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		return err
	}
	msgs, err := c.channel.Consume(queue, "", true, false, false, false, nil)
	if err != nil {
		return err
	}

	go func() {
		for d := range msgs {
			handler(d.Body)
		}
	}()
	return nil
}

func (c *Consumer) Close() {
	c.channel.Close()
	c.conn.Close()
}
