package rabbitmq

import (
	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

type Publisher struct {
	conn    *amqp.Connection
	channel *amqp.Channel
	logger  *zap.Logger
}

func NewPublisher(amqpURL string, logger *zap.Logger) (*Publisher, error) {
	conn, err := amqp.Dial(amqpURL)
	if err != nil {
		return nil, err
	}
	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}
	return &Publisher{conn: conn, channel: ch, logger: logger}, nil
}

func (p *Publisher) Publish(queue string, body []byte) error {
	_, err := p.channel.QueueDeclare(queue, true, false, false, false, nil)
	if err != nil {
		return err
	}
	return p.channel.Publish("", queue, false, false, amqp.Publishing{
		ContentType: "application/json",
		Body:        body,
	})
}

func (p *Publisher) Close() {
	p.channel.Close()
	p.conn.Close()
}
