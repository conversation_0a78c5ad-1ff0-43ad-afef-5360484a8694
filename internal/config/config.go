package config

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	ServiceName string

	// WebSocket
	WSAddr string

	// RabbitMQ
	RabbitHost  string
	RabbitPort  string
	RabbitUser  string
	RabbitPass  string
	RabbitVHost string
	RabbitURL   string

	// gRPC
	GRPCAddr string
}

func Load() *Config {
	// Load .env file if exists
	_ = godotenv.Load(".env")

	cfg := &Config{
		ServiceName: getEnv("SERVICE_NAME", "fin_gateway"),
		WSAddr:      getEnv("WS_ADDR", ":8080"),

		RabbitHost:  getEnv("RABBITMQ_HOST", "localhost"),
		RabbitPort:  getEnv("RABBITMQ_PORT", "5672"),
		RabbitUser:  getEnv("RABBITMQ_USER", "guest"),
		RabbitPass:  getEnv("RABBITMQ_PASSWORD", "guest"),
		RabbitVHost: getEnv("RABBITMQ_VHOST", "/"),

		GRPCAddr: getEnv("GRPC_ADDR", "localhost:50051"),
	}

	// Build AMQP URL
	cfg.RabbitURL = fmt.Sprintf("amqp://%s:%s@%s:%s%s",
		cfg.RabbitUser,
		cfg.RabbitPass,
		cfg.RabbitHost,
		cfg.RabbitPort,
		cfg.RabbitVHost,
	)

	return cfg
}

func getEnv(key, fallback string) string {
	if v := os.Getenv(key); v != "" {
		return v
	}
	return fallback
}
